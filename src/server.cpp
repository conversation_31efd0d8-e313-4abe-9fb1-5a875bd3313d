#include <zmq.hpp>
#include <iostream>
#include <string>
#include <unistd.h>
#include <vector>
#include <chrono>
#include <csignal> // For signal handling
#include <sys/socket.h>
#include <sys/un.h>
#include <cerrno>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <boost/asio.hpp>

#include "src/tlm_payload.capnp.h"
#include <capnp/message.h>
#include <capnp/serialize-packed.h>
#include <kj/array.h>

#include "tlm_payload.h" // For the C++ struct
#include "stats.h"

// --- Global stats object and signal handler ---
Stats deserialization_stats;
volatile bool running = true;

void signal_handler(int signum) {
    if (signum == SIGINT) {
        running = false;
    }
}
// ---

void handle_direct_message_raw(const void* data, size_t size, Stats& stats) {
    auto start = std::chrono::high_resolution_clock::now();

    // Simulate a real-world scenario where data must be copied
    // and then accessed in a structured way.
    char* local_copy = new char[size];
    memcpy(local_copy, data, size);

    // 1. Cast the copied buffer to the struct type.
    ssln::hybrid::TlmPayload* header = reinterpret_cast<ssln::hybrid::TlmPayload*>(local_copy);

    // 2. Reconstruct the data pointer.
    header->data = reinterpret_cast<uint8_t*>(local_copy + sizeof(ssln::hybrid::TlmPayload));

    // 3. Read a field to prevent optimization. A volatile variable helps ensure this.
    volatile uint64_t id = header->id;
    (void)id; // Suppress unused variable warning

    // In a real app, you'd store/use the header pointer. Here we delete it to avoid leaks.
    delete[] local_copy;

    auto end = std::chrono::high_resolution_clock::now();
    double duration_us = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    stats.add(duration_us);
}

void handle_direct_message(const zmq::message_t& request, Stats& stats) {
    handle_direct_message_raw(request.data(), request.size(), stats);
}

void handle_capnp_packed_message(const zmq::message_t& request, Stats& stats) {
    auto start = std::chrono::high_resolution_clock::now();

    auto bytes = kj::ArrayPtr<const kj::byte>(
        request.data<const kj::byte>(), 
        request.size()
    );
    kj::ArrayInputStream inputStream(bytes);
    ::capnp::PackedMessageReader reader(inputStream);
    (void)reader.getRoot<TlmPayload>();
    
    auto end = std::chrono::high_resolution_clock::now();
    double duration_us = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    stats.add(duration_us);
}

void handle_capnp_flat_message(const zmq::message_t& request, Stats& stats) {
    auto start = std::chrono::high_resolution_clock::now();

    size_t word_count = request.size() / sizeof(capnp::word);
    kj::Array<capnp::word> aligned_buffer = kj::heapArray<capnp::word>(word_count);
    memcpy(aligned_buffer.begin(), request.data(), aligned_buffer.asBytes().size());

    ::capnp::FlatArrayMessageReader reader(aligned_buffer);
    (void)reader.getRoot<TlmPayload>();

    auto end = std::chrono::high_resolution_clock::now();
    double duration_us = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    stats.add(duration_us);
}

// --- Unix Socket Helpers ---
bool read_all(int fd, void* buf, size_t size) {
    char* p = static_cast<char*>(buf);
    size_t remaining = size;
    while (remaining > 0) {
        ssize_t bytes_read = read(fd, p, remaining);
        if (bytes_read <= 0) {
            return false; // Error or client disconnected
        }
        p += bytes_read;
        remaining -= bytes_read;
    }
    return true;
}

bool write_all(int fd, const void* buf, size_t size) {
    const char* p = static_cast<const char*>(buf);
    size_t remaining = size;
    while (remaining > 0) {
        ssize_t bytes_written = write(fd, p, remaining);
        if (bytes_written < 0) {
            return false; // Error
        }
        p += bytes_written;
        remaining -= bytes_written;
    }
    return true;
}
// ---

int main (int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <mode> [print_interval]" << std::endl;
        std::cerr << "  mode: capnp-packed, capnp-flat, direct-zmq, direct-unix, direct-asio, or direct-tcp" << std::endl;
        return 1;
    }
    std::string mode = argv[1];
    int print_interval = (argc > 2) ? std::stoi(argv[2]) : 1000;

    if (mode != "capnp-packed" && mode != "capnp-flat" && mode != "direct-zmq" && mode != "direct-unix" && mode != "direct-asio" && mode != "direct-tcp") {
        std::cerr << "Invalid mode specified." << std::endl;
        return 1;
    }

    const char* socket_path = "/tmp/capnproto-test.sock";

    Stats stats;
    int request_count = 0;

    std::cout << "Server starting in " << mode << " mode. Printing stats every " << print_interval << " requests." << std::endl;
    std::cout << "(Press Ctrl+C to stop the server)" << std::endl;

    if (mode == "direct-unix") {
        int server_fd, client_fd;
        struct sockaddr_un address;
        
        // Create socket
        if ((server_fd = socket(AF_UNIX, SOCK_STREAM, 0)) == 0) {
            perror("socket failed");
            exit(EXIT_FAILURE);
        }

        // Bind socket
        address.sun_family = AF_UNIX;
        strncpy(address.sun_path, socket_path, sizeof(address.sun_path) - 1);
        unlink(socket_path); // Remove previous socket file
        if (bind(server_fd, (struct sockaddr *)&address, sizeof(address)) < 0) {
            perror("bind failed");
            exit(EXIT_FAILURE);
        }

        // Listen for connections
        if (listen(server_fd, 5) < 0) {
            perror("listen failed");
            exit(EXIT_FAILURE);
        }

        std::cout << "Unix socket server listening on " << socket_path << std::endl;

        while (true) { // Main loop to accept new connections
            std::cout << "Waiting for a new client connection..." << std::endl;
            if ((client_fd = accept(server_fd, NULL, NULL)) < 0) {
                perror("accept failed");
                // In case of non-fatal accept error, just continue
                continue;
            }
            std::cout << "Client connected." << std::endl;

            // Set larger socket buffer sizes
        int buffer_size = 8 * 1024 * 1024; // 8MB
        if (setsockopt(client_fd, SOL_SOCKET, SO_RCVBUF, &buffer_size, sizeof(buffer_size)) < 0) {
            perror("setsockopt SO_RCVBUF failed");
        }
        if (setsockopt(client_fd, SOL_SOCKET, SO_SNDBUF, &buffer_size, sizeof(buffer_size)) < 0) {
            perror("setsockopt SO_SNDBUF failed");
        }

        std::vector<char> buffer(8 * 1024 * 1024); // 8MB buffer, should be large enough
        
        while(true) {
            uint32_t msg_size;
            if (!read_all(client_fd, &msg_size, sizeof(msg_size))) {
                std::cout << "Client disconnected while reading size." << std::endl;
                break;
            }

            if (msg_size > buffer.size()) {
                std::cerr << "Error: Message size " << msg_size << " is larger than buffer " << buffer.size() << std::endl;
                break;
            }

            if (!read_all(client_fd, buffer.data(), msg_size)) {
                std::cout << "Client disconnected while reading payload." << std::endl;
                break;
            }
            
            handle_direct_message_raw(buffer.data(), msg_size, stats);

            // Echo back with framing
            if (!write_all(client_fd, &msg_size, sizeof(msg_size)) || !write_all(client_fd, buffer.data(), msg_size)) {
                std::cerr << "Error writing response to client." << std::endl;
                break;
            }

            request_count++;
            if (request_count >= print_interval) {
                std::cout << "\n--- Server Deserialization Stats (last " << print_interval << " requests) ---" << std::endl;
                stats.calculate();
                stats = Stats();
                request_count = 0;
            }
        }
        close(client_fd); // Close the connection to this specific client
        std::cout << "Client connection closed." << std::endl;
        } // End of main accept loop

        // The following lines are now theoretically unreachable unless the server is stopped with a signal
        close(server_fd);
        unlink(socket_path);
        return 0;

    } else if (mode == "direct-asio") {
        boost::asio::io_context io_context;
        boost::asio::ip::tcp::acceptor acceptor(io_context, boost::asio::ip::tcp::endpoint(boost::asio::ip::tcp::v4(), 5556));
        std::cout << "Boost.Asio server listening on port 5556" << std::endl;

        // Accept one connection and keep it alive
        boost::asio::ip::tcp::socket socket(io_context);
        acceptor.accept(socket);
        
        // Set socket options for better performance (same as client)
        boost::asio::ip::tcp::no_delay no_delay_option(true);
        socket.set_option(no_delay_option);
        
        boost::asio::socket_base::send_buffer_size send_buffer_option(8 * 1024 * 1024);
        boost::asio::socket_base::receive_buffer_size recv_buffer_option(8 * 1024 * 1024);
        socket.set_option(send_buffer_option);
        socket.set_option(recv_buffer_option);
        
        std::cout << "Client connected via Boost.Asio." << std::endl;
        
        std::vector<char> buffer(8 * 1024 * 1024); // 8MB buffer

        while(true) {
            boost::system::error_code error;
            uint32_t msg_size;

            size_t len = boost::asio::read(socket, boost::asio::buffer(&msg_size, sizeof(msg_size)), error);
            if (error == boost::asio::error::eof || len == 0) {
                std::cout << "Client disconnected." << std::endl;
                break; // Connection closed cleanly by peer.
            } else if (error) {
                std::cerr << "Read error: " << error.message() << std::endl;
                break;
            }

            if (msg_size > buffer.size()) {
                std::cerr << "Error: Message size " << msg_size << " is larger than buffer " << buffer.size() << std::endl;
                break;
            }

            boost::asio::read(socket, boost::asio::buffer(buffer.data(), msg_size));
            handle_direct_message_raw(buffer.data(), msg_size, stats);

            // Use scatter-gather I/O to send size and data in one operation
            std::vector<boost::asio::const_buffer> send_buffers;
            send_buffers.push_back(boost::asio::buffer(&msg_size, sizeof(msg_size)));
            send_buffers.push_back(boost::asio::buffer(buffer.data(), msg_size));
            boost::asio::write(socket, send_buffers);

            request_count++;
            if (request_count >= print_interval) {
                std::cout << "\n--- Server Deserialization Stats (last " << print_interval << " requests) ---" << std::endl;
                stats.calculate();
                stats = Stats();
                request_count = 0;
            }
        }
        return 0;
    } else if (mode == "direct-tcp") {
        int server_fd, client_fd;
        struct sockaddr_in address;

        // Create socket
        if ((server_fd = socket(AF_INET, SOCK_STREAM, 0)) == 0) {
            perror("TCP socket failed");
            exit(EXIT_FAILURE);
        }

        // Set socket options
        int opt = 1;
        if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            perror("setsockopt SO_REUSEADDR failed");
        }

        // Bind socket
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(5557);

        if (bind(server_fd, (struct sockaddr *)&address, sizeof(address)) < 0) {
            perror("TCP bind failed");
            exit(EXIT_FAILURE);
        }

        // Listen for connections
        if (listen(server_fd, 5) < 0) {
            perror("TCP listen failed");
            exit(EXIT_FAILURE);
        }

        std::cout << "TCP server listening on port 5557" << std::endl;

        while (true) { // Main loop to accept new connections
            std::cout << "Waiting for a new TCP client connection..." << std::endl;
            if ((client_fd = accept(server_fd, NULL, NULL)) < 0) {
                perror("TCP accept failed");
                continue;
            }
            std::cout << "TCP client connected." << std::endl;

            // Set TCP_NODELAY
            int flag = 1;
            if (setsockopt(client_fd, IPPROTO_TCP, TCP_NODELAY, &flag, sizeof(flag)) < 0) {
                perror("setsockopt TCP_NODELAY failed");
            }

            // Set larger socket buffer sizes
            int buffer_size = 8 * 1024 * 1024; // 8MB
            if (setsockopt(client_fd, SOL_SOCKET, SO_RCVBUF, &buffer_size, sizeof(buffer_size)) < 0) {
                perror("setsockopt SO_RCVBUF failed");
            }
            if (setsockopt(client_fd, SOL_SOCKET, SO_SNDBUF, &buffer_size, sizeof(buffer_size)) < 0) {
                perror("setsockopt SO_SNDBUF failed");
            }

            std::vector<char> buffer(8 * 1024 * 1024); // 8MB buffer

            while(true) {
                uint32_t msg_size;
                if (!read_all(client_fd, &msg_size, sizeof(msg_size))) {
                    std::cout << "TCP client disconnected while reading size." << std::endl;
                    break;
                }

                if (msg_size > buffer.size()) {
                    std::cerr << "Error: TCP message size " << msg_size << " is larger than buffer " << buffer.size() << std::endl;
                    break;
                }

                if (!read_all(client_fd, buffer.data(), msg_size)) {
                    std::cout << "TCP client disconnected while reading payload." << std::endl;
                    break;
                }

                handle_direct_message_raw(buffer.data(), msg_size, stats);

                // Echo back with framing
                if (!write_all(client_fd, &msg_size, sizeof(msg_size)) || !write_all(client_fd, buffer.data(), msg_size)) {
                    std::cerr << "Error writing response to TCP client." << std::endl;
                    break;
                }

                request_count++;
                if (request_count >= print_interval) {
                    std::cout << "\n--- Server Deserialization Stats (last " << print_interval << " requests) ---" << std::endl;
                    stats.calculate();
                    stats = Stats();
                    request_count = 0;
                }
            }
            close(client_fd); // Close the connection to this specific client
            std::cout << "TCP client connection closed." << std::endl;
        } // End of main accept loop

        close(server_fd);
        return 0;
    }


    // ZMQ modes
    zmq::context_t context (1);
    zmq::socket_t socket (context, ZMQ_REP);
    socket.bind ("tcp://*:5555");

    while (true) {
        zmq::message_t request;
        (void)socket.recv (request, zmq::recv_flags::none);

        if (mode == "direct-zmq") {
            handle_direct_message(request, stats);
        } else if (mode == "capnp-packed") {
            handle_capnp_packed_message(request, stats);
        } else { // capnp-flat
            handle_capnp_flat_message(request, stats);
        }

        socket.send (request, zmq::send_flags::none);

        request_count++;
        if (request_count >= print_interval) {
            std::cout << "\n--- Server Deserialization Stats (last " << print_interval << " requests) ---" << std::endl;
            stats.calculate();
            // Reset for next batch
            stats = Stats();
            request_count = 0;
        }
    }

    return 0;
}
